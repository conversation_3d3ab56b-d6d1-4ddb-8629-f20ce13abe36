//
// Copyright (C) 2025, NinjaTrader LLC <www.ninjatrader.com>.
// NinjaTrader reserves the right to modify or overwrite this NinjaScript component with each release.
//
#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

//This namespace holds strategies in this folder and is required. Do not change it.
namespace NinjaTrader.NinjaScript.Strategies
{
	public class FibonacciPerBarStrategy : Strategy
	{
		private double[] fibLevels = { 1.1, 1.08, 1.0, 0.9, 0.1, 0.0, -0.08, -0.1 };
		private Brush[] fibColors;

		// Multi-timeframe variables
		private DateTime lastMtfBarTime;
		private bool isNewMtfBar;
		private double mtfHigh, mtfLow, mtfOpen, mtfClose;
		private bool mtfDataInitialized;
		private int lastProcessedBar = -1;

		// MTF Candle display variables
		private List<string> mtfCandleObjects = new List<string>();
		private const int MaxMtfCandles = 50;

		// Trading variables
		private double greenLinePrice = 0;  // 1.0 level (buy signal)
		private double redLinePrice = 0;    // 0.0 level (sell signal)
		private double longTpPrice = 0;     // 1.1 level (long take profit)
		private double shortTpPrice = 0;    // -0.1 level (short take profit)
		private bool levelsCalculated = false;

		// Enhanced signal tracking variables
		private bool greenSignalTriggered = false;
		private bool redSignalTriggered = false;
		private bool activeBuyPosition = false;
		private bool activeSellPosition = false;
		private double buyTpLevel = double.NaN;
		private double sellTpLevel = double.NaN;

		// Previous levels for delayed signals (MTF mode)
		private double prevGreenLevel = double.NaN;
		private double prevRedLevel = double.NaN;
		private double currentGreenLevel = double.NaN;
		private double currentRedLevel = double.NaN;
		private bool htfJustClosed = false;

		// Signal tracking for display mode (legacy)
		private bool lastLongSignal = false;
		private bool lastShortSignal = false;

		// Debug identifier for this strategy instance
		private string strategyId = "";
		
		protected override void OnStateChange()
		{
			if (State == State.SetDefaults)
			{
				Description					= @"Fibonacci strategy that buys at green line (1.0) and sells at red line (0.0)";
				Name						= "FibonacciPerBarStrategy";
				Calculate					= Calculate.OnBarClose;
				IsOverlay					= true;
				DisplayInDataBox			= true;
				DrawOnPricePanel			= true;
				ScaleJustification			= NinjaTrader.Gui.Chart.ScaleJustification.Right;
				
				// Strategy specific settings
				EntriesPerDirection			= 1;
				EntryHandling				= EntryHandling.AllEntries;
				IsExitOnSessionCloseStrategy = true;
				ExitOnSessionCloseSeconds	= 30;
				IsInstantiatedOnEachOptimizationIteration = true;
				
				// Input parameters
				ShowLevels					= true;
				ShowLabels					= true;
				LineWidth					= 1;
				LineTransparency			= 20;

				// Trading parameters
				SignalDisplayMode			= false;
				ShowSignals					= true;
				ShowImmediateSignals		= true;
				ShowDelayedSignals			= true;

				// Multi-timeframe parameters
				UseMtfMode					= false;
				MtfTimeframe				= 5;
				MtfPeriodType				= BarsPeriodType.Minute;
				UseConfirmedData			= true;

				// MTF Candle display parameters
				ShowMtfCandles				= false;
				ShowCandleInfo				= false;
				CandleBullishColor			= Brushes.Green;
				CandleBearishColor			= Brushes.Red;
				CandleWickColor				= Brushes.Black;
				CandleBorderColor			= Brushes.Black;
				CandleTransparency			= 20;

				// Color inputs
				FibColor11					= Brushes.Black;
				FibColor108					= Brushes.Black;
				FibColor10					= Brushes.Green;
				FibColor09					= Brushes.Orange;
				FibColor01					= Brushes.Purple;
				FibColor00					= Brushes.Red;
				FibColorNeg08				= Brushes.Black;
				FibColorNeg1				= Brushes.Black;
				TextColor					= Brushes.Black;
			}
			else if (State == State.Configure)
			{
				// Add multi-timeframe data series if MTF mode is enabled
				if (UseMtfMode)
				{
					try
					{
						AddDataSeries(MtfPeriodType, MtfTimeframe);
						Print($"Added MTF data series: {MtfTimeframe} {MtfPeriodType}");
					}
					catch (Exception ex)
					{
						Print($"Error adding MTF data series: {ex.Message}");
					}
				}
			}
			else if (State == State.DataLoaded)
			{
				// Initialize color array
				fibColors = new Brush[]
				{
					FibColor11, FibColor108, FibColor10, FibColor09,
					FibColor01, FibColor00, FibColorNeg08, FibColorNeg1
				};

				// Create unique identifier for this strategy instance
				strategyId = $"{Instrument.FullName}_{BarsPeriod.BarsPeriodType}{BarsPeriod.Value}_{DateTime.Now.Ticks}";

				// Reset MTF state for chart switching - this ensures clean state for each chart instance
				ResetMtfState();

				// Additional debug logging to help identify chart switching issues
				Print($"[{strategyId}] Strategy initialized on chart: {Instrument.FullName} - {BarsPeriod.BarsPeriodType} {BarsPeriod.Value}");
			}
			else if (State == State.Terminated)
			{
				// Clean up when strategy is removed
				ResetMtfState();
			}
		}

		protected override void OnBarUpdate()
		{
			if (CurrentBar < 1)
				return;

			// Handle multi-timeframe mode
			if (UseMtfMode)
			{
				// Only process primary bars (chart timeframe) for drawing
				if (BarsInProgress != 0)
					return;

				// Check if we have enough bars on both timeframes
				if (CurrentBars[0] < 1 || (BarsArray.Length > 1 && CurrentBars[1] < 1))
					return;

				// Validate MTF data series exists
				if (BarsArray.Length < 2)
				{
					Print("Error: MTF data series not properly initialized");
					return;
				}

				// Reset state if we're processing a different bar than before (chart switching)
				if (lastProcessedBar != CurrentBar)
				{
					if (!mtfDataInitialized)
					{
						ResetMtfState();
						mtfDataInitialized = true;
					}
					lastProcessedBar = CurrentBar;
				}

				// Detect new MTF bar
				DateTime currentMtfTime = Times[1][0];
				isNewMtfBar = currentMtfTime != lastMtfBarTime;

				// Pine Script equivalent: draw_condition = use_confirmed_data ? true : barstate.isconfirmed
				// In NinjaTrader: UseConfirmedData OR Calculate == Calculate.OnBarClose
				bool drawCondition = UseConfirmedData || (Calculate == Calculate.OnBarClose);

				// Get MTF OHLC data based on confirmed data setting
				int mtfIndex = UseConfirmedData ? 1 : 0; // Use previous bar if confirmed data is enabled
				if (CurrentBars[1] >= mtfIndex)
				{
					try
					{
						mtfOpen = Opens[1][mtfIndex];
						mtfHigh = Highs[1][mtfIndex];
						mtfLow = Lows[1][mtfIndex];
						mtfClose = Closes[1][mtfIndex];

						// Validate MTF data
						if (mtfHigh <= 0 || mtfLow <= 0 || mtfHigh < mtfLow)
						{
							return; // Skip invalid data
						}
					}
					catch (Exception ex)
					{
						Print($"Error accessing MTF data: {ex.Message}");
						return;
					}
				}
				else
				{
					return; // Not enough MTF data yet
				}

				// Update lastMtfBarTime when we detect a new MTF bar
				if (isNewMtfBar)
				{
					lastMtfBarTime = currentMtfTime;
				}

				// Only draw when draw condition is met AND we have a new MTF period
				if (!drawCondition || !isNewMtfBar)
				{
					return;
				}

				// Calculate price range from MTF data
				double rangeHigh = mtfHigh;
				double rangeLow = mtfLow;
				double priceRange = rangeHigh - rangeLow;

				if (priceRange <= 0)
					return;

				// Calculate trading levels
				CalculateTradingLevels(rangeLow, priceRange);

				// Draw Fibonacci levels for MTF mode
				DrawFibonacciLevels(rangeLow, priceRange, true);

				// Draw MTF candle if enabled
				if (ShowMtfCandles && isNewMtfBar)
				{
					DrawMtfCandle();
				}

				// Execute trading logic
				ExecuteTradingLogic();
			}
			else
			{
				// Original per-bar mode
				// Calculate price range for current bar
				double rangeHigh = High[0];
				double rangeLow = Low[0];
				double priceRange = rangeHigh - rangeLow;

				if (priceRange <= 0)
					return;

				// Calculate trading levels
				CalculateTradingLevels(rangeLow, priceRange);

				// Draw Fibonacci levels for per-bar mode
				DrawFibonacciLevels(rangeLow, priceRange, false);

				// Execute trading logic
				ExecuteTradingLogic();
			}
		}

		private void CalculateTradingLevels(double rangeLow, double priceRange)
		{
			// Calculate new Fibonacci levels
			double newGreenLevel = rangeLow + (priceRange * 1.0);   // 1.0 level (buy signal)
			double newRedLevel = rangeLow + (priceRange * 0.0);     // 0.0 level (sell signal)
			double newLongTpPrice = rangeLow + (priceRange * 1.1);      // 1.1 level (long take profit)
			double newShortTpPrice = rangeLow + (priceRange * -0.1);    // -0.1 level (short take profit)

			// Check if levels have actually changed (to avoid unnecessary resets)
			bool levelsChanged = Math.Abs(newGreenLevel - greenLinePrice) > TickSize * 0.1 ||
								Math.Abs(newRedLevel - redLinePrice) > TickSize * 0.1;

			// Store previous levels before updating (for MTF delayed signals)
			if (levelsChanged && !double.IsNaN(currentGreenLevel) && !double.IsNaN(currentRedLevel))
			{
				prevGreenLevel = currentGreenLevel;
				prevRedLevel = currentRedLevel;
				htfJustClosed = true;
			}

			// Update levels
			greenLinePrice = newGreenLevel;
			redLinePrice = newRedLevel;
			longTpPrice = newLongTpPrice;
			shortTpPrice = newShortTpPrice;
			currentGreenLevel = greenLinePrice;
			currentRedLevel = redLinePrice;

			// Only reset signal flags if levels actually changed
			if (levelsChanged)
			{
				greenSignalTriggered = false;
				redSignalTriggered = false;
				Print($"[{strategyId}] Levels changed - Reset signal flags. Green: {greenLinePrice:F3}, Red: {redLinePrice:F3}");
			}

			levelsCalculated = true;
		}

		private void ExecuteTradingLogic()
		{
			if (!levelsCalculated)
				return;

			// Check for take profit conditions first
			CheckTakeProfitConditions();

			// Check for enhanced signal conditions
			CheckEnhancedSignalConditions();

			// Check for delayed signals (MTF mode only)
			if (UseMtfMode)
			{
				CheckDelayedSignalConditions();
			}

		}

		// Enhanced signal detection method - checks if bar actually touches the Fibonacci levels
		private void CheckEnhancedSignalConditions()
		{
			if (!ShowSignals || !ShowImmediateSignals)
			{
				// Debug: Log why signals are disabled
				if (!ShowSignals)
					Print($"[{strategyId}] Signals disabled - ShowSignals: {ShowSignals}");
				if (!ShowImmediateSignals)
					Print($"[{strategyId}] Immediate signals disabled - ShowImmediateSignals: {ShowImmediateSignals}");
				return;
			}

			// Check for buy signal: high >= green_level and low <= green_level (bar touches green line)
			bool buySignal = !double.IsNaN(greenLinePrice) && !double.IsNaN(redLinePrice) &&
							High[0] >= greenLinePrice && Low[0] <= greenLinePrice && !greenSignalTriggered;

			// Check for sell signal: low <= red_level and high >= red_level (bar touches red line)
			bool sellSignal = !double.IsNaN(greenLinePrice) && !double.IsNaN(redLinePrice) &&
							 Low[0] <= redLinePrice && High[0] >= redLinePrice && !redSignalTriggered;

			// Debug logging for signal conditions
			if (CurrentBar % 100 == 0) // Log every 100 bars to avoid spam
			{
				Print($"[{strategyId}] Bar {CurrentBar}: Green={greenLinePrice:F3}, Red={redLinePrice:F3}, High={High[0]:F3}, Low={Low[0]:F3}");
				Print($"[{strategyId}] GreenTriggered={greenSignalTriggered}, RedTriggered={redSignalTriggered}, LevelsCalc={levelsCalculated}");
			}

			if (SignalDisplayMode)
			{
				// Signal display mode - only show signals, don't execute trades
				if (buySignal)
				{
					CreateSignalArrow("BUY", greenLinePrice, false);
					greenSignalTriggered = true;
				}

				if (sellSignal)
				{
					CreateSignalArrow("SELL", redLinePrice, false);
					redSignalTriggered = true;
				}
			}
			else
			{
				// Normal trading mode - execute actual trades
				if (Position.MarketPosition == MarketPosition.Flat && buySignal)
				{
					EnterLong("Long Entry");
					CreateSignalArrow("BUY", greenLinePrice, false);
					greenSignalTriggered = true;

					// Update position tracking
					activeBuyPosition = true;
					activeSellPosition = false;
					buyTpLevel = longTpPrice;
					sellTpLevel = double.NaN;

					// Check if TP is hit on the same bar as the buy signal
					if (High[0] >= buyTpLevel && Low[0] <= buyTpLevel)
					{
						ExitLong("Long TP Same Bar");
						CreateSignalArrow("TP", buyTpLevel, false);
						activeBuyPosition = false;
						buyTpLevel = double.NaN;
					}
				}

				if (Position.MarketPosition == MarketPosition.Flat && sellSignal)
				{
					EnterShort("Short Entry");
					CreateSignalArrow("SELL", redLinePrice, false);
					redSignalTriggered = true;

					// Update position tracking
					activeSellPosition = true;
					activeBuyPosition = false;
					sellTpLevel = shortTpPrice;
					buyTpLevel = double.NaN;

					// Check if TP is hit on the same bar as the sell signal
					if (Low[0] <= sellTpLevel && High[0] >= sellTpLevel)
					{
						ExitShort("Short TP Same Bar");
						CreateSignalArrow("TP", sellTpLevel, false);
						activeSellPosition = false;
						sellTpLevel = double.NaN;
					}
				}
			}
		}

		// Check for delayed signals using previous Fibonacci levels (MTF mode)
		private void CheckDelayedSignalConditions()
		{
			if (!ShowSignals || !ShowDelayedSignals || !htfJustClosed)
				return;

			// Use the previous fib levels for delayed signals to prevent repainting
			double signalGreen = !double.IsNaN(prevGreenLevel) ? prevGreenLevel : currentGreenLevel;
			double signalRed = !double.IsNaN(prevRedLevel) ? prevRedLevel : currentRedLevel;

			if (double.IsNaN(signalGreen) || double.IsNaN(signalRed))
				return;

			// Check if current bar hits previous green level (buy signal) and signal not already triggered
			bool delayedBuySignal = High[0] >= signalGreen && Low[0] <= signalGreen && !greenSignalTriggered;
			bool delayedSellSignal = Low[0] <= signalRed && High[0] >= signalRed && !redSignalTriggered;

			if (delayedBuySignal)
			{
				if (!SignalDisplayMode && Position.MarketPosition == MarketPosition.Flat)
				{
					EnterLong("Long Entry Delayed");
				}
				CreateSignalArrow("BUY", signalGreen, true);
				greenSignalTriggered = true;
				htfJustClosed = false; // Reset flag after signal

				// Update position tracking
				activeBuyPosition = true;
				activeSellPosition = false;
				buyTpLevel = CalculateTpLevel(signalRed, signalGreen - signalRed, true); // Long TP
				sellTpLevel = double.NaN;

				// Check if TP is hit on the same bar as the buy signal
				if (High[0] >= buyTpLevel && Low[0] <= buyTpLevel)
				{
					if (!SignalDisplayMode && Position.MarketPosition == MarketPosition.Long)
					{
						ExitLong("Long TP Same Bar Delayed");
					}
					CreateSignalArrow("TP", buyTpLevel, false);
					activeBuyPosition = false;
					buyTpLevel = double.NaN;
				}
			}

			if (delayedSellSignal)
			{
				if (!SignalDisplayMode && Position.MarketPosition == MarketPosition.Flat)
				{
					EnterShort("Short Entry Delayed");
				}
				CreateSignalArrow("SELL", signalRed, true);
				redSignalTriggered = true;
				htfJustClosed = false; // Reset flag after signal

				// Update position tracking
				activeSellPosition = true;
				activeBuyPosition = false;
				sellTpLevel = CalculateTpLevel(signalRed, signalGreen - signalRed, false); // Short TP
				buyTpLevel = double.NaN;

				// Check if TP is hit on the same bar as the sell signal
				if (Low[0] <= sellTpLevel && High[0] >= sellTpLevel)
				{
					if (!SignalDisplayMode && Position.MarketPosition == MarketPosition.Short)
					{
						ExitShort("Short TP Same Bar Delayed");
					}
					CreateSignalArrow("TP", sellTpLevel, false);
					activeSellPosition = false;
					sellTpLevel = double.NaN;
				}
			}
		}

		// Helper method to calculate take profit levels
		private double CalculateTpLevel(double rangeLow, double priceRange, bool isLong)
		{
			if (isLong)
				return rangeLow + (priceRange * 1.1);   // 1.1 level for buy take profit
			else
				return rangeLow + (priceRange * -0.1); // -0.1 level for sell take profit
		}

		// Check for take profit conditions on every bar
		private void CheckTakeProfitConditions()
		{
			// Check for long take profit
			if (activeBuyPosition && !double.IsNaN(buyTpLevel))
			{
				bool buyTpHit = High[0] >= buyTpLevel && Low[0] <= buyTpLevel;
				if (buyTpHit)
				{
					if (!SignalDisplayMode && Position.MarketPosition == MarketPosition.Long)
					{
						ExitLong("Long TP");
					}
					CreateSignalArrow("TP", buyTpLevel, false);
					activeBuyPosition = false;
					buyTpLevel = double.NaN;
				}
			}

			// Check for short take profit
			if (activeSellPosition && !double.IsNaN(sellTpLevel))
			{
				bool sellTpHit = Low[0] <= sellTpLevel && High[0] >= sellTpLevel;
				if (sellTpHit)
				{
					if (!SignalDisplayMode && Position.MarketPosition == MarketPosition.Short)
					{
						ExitShort("Short TP");
					}
					CreateSignalArrow("TP", sellTpLevel, false);
					activeSellPosition = false;
					sellTpLevel = double.NaN;
				}
			}
		}

		// Create signal arrow similar to Pine Script's create_signal_dot function
		private void CreateSignalArrow(string signalType, double priceLevel, bool isDelayed)
		{
			string signalName = $"{signalType}Signal_{CurrentBar}_{Time[0].Ticks}";
			string labelName = $"{signalType}Label_{CurrentBar}_{Time[0].Ticks}";

			switch (signalType)
			{
				case "BUY":
					// Position arrow to the left of the current candle horizontally
					Draw.ArrowUp(this, signalName, true, 1, priceLevel, Brushes.Green);

					// Add label under the arrow
					string buyLabelText = isDelayed ? "BUY\n(Delayed)" : "BUY\n(Immediate)";
					double buyLabelY = priceLevel - (TickSize * 15); // Position below the arrow
					Draw.Text(this, labelName, buyLabelText, 1, buyLabelY, Brushes.Green);

					Print($"BUY{(isDelayed ? " (Delayed)" : "")} signal at {Time[0]}: Price level {priceLevel:F3} on bar {CurrentBar}");
					break;

				case "SELL":
					Draw.ArrowDown(this, signalName, true, 1, priceLevel, Brushes.Red);

					// Add label above the arrow
					string sellLabelText = isDelayed ? "SELL\n(Delayed)" : "SELL\n(Immediate)";
					double sellLabelY = priceLevel + (TickSize * 15); // Position above the arrow
					Draw.Text(this, labelName, sellLabelText, 1, sellLabelY, Brushes.Red);

					Print($"SELL{(isDelayed ? " (Delayed)" : "")} signal at {Time[0]}: Price level {priceLevel:F3} on bar {CurrentBar}");
					break;

				case "TP":
					Draw.Diamond(this, signalName, true, 1, priceLevel, Brushes.LimeGreen);

					// Add label next to the diamond
					Draw.Text(this, labelName, "TP", 1, priceLevel + (TickSize * 5), Brushes.LimeGreen);

					Print($"TP signal at {Time[0]}: Price level {priceLevel:F3} on bar {CurrentBar}");
					break;
			}
		}

		private void ResetMtfState()
		{
			lastMtfBarTime = DateTime.MinValue;
			isNewMtfBar = false;
			mtfHigh = mtfLow = mtfOpen = mtfClose = 0;
			mtfDataInitialized = false;
			lastProcessedBar = -1;

			// Reset ALL signal tracking variables (important for chart switching)
			levelsCalculated = false;
			greenSignalTriggered = false;
			redSignalTriggered = false;
			activeBuyPosition = false;
			activeSellPosition = false;
			buyTpLevel = double.NaN;
			sellTpLevel = double.NaN;
			prevGreenLevel = double.NaN;
			prevRedLevel = double.NaN;
			currentGreenLevel = double.NaN;
			currentRedLevel = double.NaN;
			htfJustClosed = false;

			// Reset trading levels
			greenLinePrice = 0;
			redLinePrice = 0;
			longTpPrice = 0;
			shortTpPrice = 0;

			// Reset legacy signal tracking
			lastLongSignal = false;
			lastShortSignal = false;

			// Clean up any existing MTF candle objects
			foreach (string objectName in mtfCandleObjects)
			{
				try
				{
					RemoveDrawObject(objectName);
				}
				catch
				{
					// Ignore errors if object doesn't exist
				}
			}
			mtfCandleObjects.Clear();
		}

		private void DrawFibonacciLevels(double rangeLow, double priceRange, bool isMtfMode)
		{
			if (!ShowLevels)
				return;

			for (int i = 0; i < fibLevels.Length; i++)
			{
				double fibLevel = fibLevels[i];
				double fibPrice = rangeLow + (priceRange * fibLevel);

				// Create unique line names for MTF vs per-bar mode
				string lineName = isMtfMode ?
					$"MTFFibLine_{lastMtfBarTime.Ticks}_{i}" :
					$"FibLine_{CurrentBar}_{i}";

				// For MTF mode, draw lines spanning the MTF timeframe duration
				if (isMtfMode)
				{
					// Calculate the number of chart bars in the MTF timeframe
					int mtfDurationBars = CalculateMtfDurationInBars();

					// Calculate position offset based on confirmed data setting
					// This matches the HTF candle positioning logic
					int positionOffset = UseConfirmedData ? mtfDurationBars : 0;

					// Calculate line start and end positions to match HTF candle positioning
					int lineStartBarsAgo = mtfDurationBars - 1 + positionOffset;
					int lineEndBarsAgo = positionOffset;

					// Draw line spanning the MTF period with proper positioning
					Draw.Line(this, lineName, lineStartBarsAgo, fibPrice, lineEndBarsAgo, fibPrice,
						GetColorWithTransparency(fibColors[i], LineTransparency));
				}
				else
				{
					// Original per-bar mode: line for current bar only
					Draw.Line(this, lineName, 0, fibPrice, 1, fibPrice,
						GetColorWithTransparency(fibColors[i], LineTransparency));
				}

				// Add label if enabled
				if (ShowLabels)
				{
					string labelName = isMtfMode ?
						$"MTFFibLabel_{lastMtfBarTime.Ticks}_{i}" :
						$"FibLabel_{CurrentBar}_{i}";
					string labelText = $"{fibLevel:F2} ({fibPrice:F3})";

					// Position label at the end of the line (matching the line positioning)
					int labelPosition = isMtfMode ?
						(UseConfirmedData ? CalculateMtfDurationInBars() : 0) : 0;

					Draw.Text(this, labelName, labelText, labelPosition, fibPrice, TextColor);
				}
			}
		}

		private int CalculateMtfDurationInBars()
		{
			if (!UseMtfMode || BarsArray.Length < 2)
				return 1;

			try
			{
				// Calculate approximate number of chart bars in one MTF bar
				// This is a simplified calculation - in practice, this can vary
				TimeSpan chartBarDuration = GetBarDuration(BarsArray[0].BarsPeriod);
				TimeSpan mtfBarDuration = GetBarDuration(BarsArray[1].BarsPeriod);

				if (chartBarDuration.TotalSeconds > 0 && mtfBarDuration.TotalSeconds > 0)
				{
					double ratio = mtfBarDuration.TotalSeconds / chartBarDuration.TotalSeconds;
					int barsInMtf = (int)Math.Round(ratio);

					// Ensure reasonable bounds
					return Math.Max(1, Math.Min(barsInMtf, 1000));
				}
			}
			catch (Exception ex)
			{
				Print($"Error calculating MTF duration: {ex.Message}");
			}

			return 1;
		}

		private TimeSpan GetBarDuration(BarsPeriod barsPeriod)
		{
			switch (barsPeriod.BarsPeriodType)
			{
				case BarsPeriodType.Second:
					return TimeSpan.FromSeconds(barsPeriod.Value);
				case BarsPeriodType.Minute:
					return TimeSpan.FromMinutes(barsPeriod.Value);
				case BarsPeriodType.Day:
					return TimeSpan.FromDays(barsPeriod.Value);
				case BarsPeriodType.Week:
					return TimeSpan.FromDays(barsPeriod.Value * 7);
				case BarsPeriodType.Month:
					return TimeSpan.FromDays(barsPeriod.Value * 30); // Approximate
				default:
					return TimeSpan.FromMinutes(1); // Default fallback
			}
		}

		private void DrawMtfCandle()
		{
			if (!UseMtfMode || mtfHigh <= 0 || mtfLow <= 0 || mtfHigh < mtfLow)
				return;

			// Clean up old candles first
			CleanupOldMtfCandles();

			// Calculate MTF duration in bars for positioning
			int mtfDurationBars = CalculateMtfDurationInBars();

			// Calculate position offset based on confirmed data setting
			// When using confirmed data, we need to shift the candle back by the MTF duration
			// because the data is from the previous MTF bar
			int positionOffset = UseConfirmedData ? mtfDurationBars : 0;

			// Determine candle color based on open/close relationship
			bool isBullish = mtfClose > mtfOpen;
			Brush candleColor = isBullish ? CandleBullishColor : CandleBearishColor;

			// Calculate candle body coordinates
			double bodyTop = Math.Max(mtfOpen, mtfClose);
			double bodyBottom = Math.Min(mtfOpen, mtfClose);

			// Create unique names for this MTF candle
			string candleBodyName = $"MTFCandleBody_{lastMtfBarTime.Ticks}";
			string upperWickName = $"MTFUpperWick_{lastMtfBarTime.Ticks}";
			string lowerWickName = $"MTFLowerWick_{lastMtfBarTime.Ticks}";
			string candleInfoName = $"MTFCandleInfo_{lastMtfBarTime.Ticks}";

			// Calculate candle start and end positions
			int candleStartBarsAgo = mtfDurationBars - 1 + positionOffset;
			int candleEndBarsAgo = positionOffset;

			// Draw candle body using rectangle
			Draw.Rectangle(this, candleBodyName, false, candleStartBarsAgo, bodyBottom, candleEndBarsAgo, bodyTop,
				GetColorWithTransparency(CandleBorderColor, 0),
				GetColorWithTransparency(candleColor, CandleTransparency), CandleTransparency);
			mtfCandleObjects.Add(candleBodyName);

			// Calculate wick position (center of the MTF period)
			int wickPosition = (mtfDurationBars / 2) + positionOffset;

			// Draw upper wick if it exists
			if (mtfHigh > bodyTop)
			{
				Draw.Line(this, upperWickName, wickPosition, bodyTop, wickPosition, mtfHigh,
					GetColorWithTransparency(CandleWickColor, 0));
				mtfCandleObjects.Add(upperWickName);
			}

			// Draw lower wick if it exists
			if (mtfLow < bodyBottom)
			{
				Draw.Line(this, lowerWickName, wickPosition, bodyBottom, wickPosition, mtfLow,
					GetColorWithTransparency(CandleWickColor, 0));
				mtfCandleObjects.Add(lowerWickName);
			}

			// Add candle info label if enabled
			if (ShowCandleInfo)
			{
				double priceRange = mtfHigh - mtfLow;
				string candleInfo = $"MTF {MtfTimeframe}{GetPeriodTypeShortName(MtfPeriodType)} Candle\n" +
								   $"O: {mtfOpen:F2}\n" +
								   $"H: {mtfHigh:F2}\n" +
								   $"L: {mtfLow:F2}\n" +
								   $"C: {mtfClose:F2}\n" +
								   $"Range: {priceRange:F2}";

				// Position label above the candle
				double labelY = mtfHigh + (priceRange * 0.02);
				Draw.Text(this, candleInfoName, candleInfo, wickPosition, labelY, TextColor);
				mtfCandleObjects.Add(candleInfoName);
			}
		}

		private void CleanupOldMtfCandles()
		{
			// Remove old candle objects if we exceed the maximum
			while (mtfCandleObjects.Count > MaxMtfCandles * 4) // 4 objects per candle (body, 2 wicks, label)
			{
				string oldObjectName = mtfCandleObjects[0];
				mtfCandleObjects.RemoveAt(0);

				// Try to remove the drawing object
				try
				{
					RemoveDrawObject(oldObjectName);
				}
				catch
				{
					// Ignore errors if object doesn't exist
				}
			}
		}

		private string GetPeriodTypeShortName(BarsPeriodType periodType)
		{
			switch (periodType)
			{
				case BarsPeriodType.Second: return "s";
				case BarsPeriodType.Minute: return "m";
				case BarsPeriodType.Day: return "d";
				case BarsPeriodType.Week: return "w";
				case BarsPeriodType.Month: return "M";
				default: return "";
			}
		}

		private Brush GetColorWithTransparency(Brush originalBrush, int transparency)
		{
			if (originalBrush is SolidColorBrush solidBrush)
			{
				Color color = solidBrush.Color;
				byte alpha = (byte)(255 * (100 - transparency) / 100);
				return new SolidColorBrush(Color.FromArgb(alpha, color.R, color.G, color.B));
			}
			return originalBrush;
		}

		#region Properties
		[NinjaScriptProperty]
		[Display(Name="Use Multi-Timeframe Mode", Description="Enable multi-timeframe Fibonacci levels", Order=1, GroupName="Multi-Timeframe Settings")]
		public bool UseMtfMode
		{ get; set; }

		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="MTF Timeframe Value", Description="Multi-timeframe period value", Order=2, GroupName="Multi-Timeframe Settings")]
		public int MtfTimeframe
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name="MTF Period Type", Description="Multi-timeframe period type", Order=3, GroupName="Multi-Timeframe Settings")]
		public BarsPeriodType MtfPeriodType
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name="Use Confirmed Data", Description="Use confirmed MTF data (no repaint)", Order=4, GroupName="Multi-Timeframe Settings")]
		public bool UseConfirmedData
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name="Show MTF Candles", Description="Display the multi-timeframe candle that Fibonacci levels are based on", Order=5, GroupName="Multi-Timeframe Settings")]
		public bool ShowMtfCandles
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name="Show Candle Info", Description="Show MTF candle OHLC information", Order=6, GroupName="Multi-Timeframe Settings")]
		public bool ShowCandleInfo
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name="Show Levels", Description="Show Fibonacci Levels", Order=1, GroupName="Display Settings")]
		public bool ShowLevels
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name="Show Labels", Description="Show Level Labels", Order=2, GroupName="Display Settings")]
		public bool ShowLabels
		{ get; set; }

		[NinjaScriptProperty]
		[Range(1, 5)]
		[Display(Name="Line Width", Description="Line Width", Order=3, GroupName="Display Settings")]
		public int LineWidth
		{ get; set; }

		[NinjaScriptProperty]
		[Range(0, 100)]
		[Display(Name="Line Transparency", Description="Line Transparency", Order=4, GroupName="Display Settings")]
		public int LineTransparency
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name="Signal Display Mode", Description="Show signals without executing trades (for testing/visualization)", Order=1, GroupName="Trading Settings")]
		public bool SignalDisplayMode
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name="Show Buy/Sell Signals", Description="Enable signal generation and display", Order=2, GroupName="Trading Settings")]
		public bool ShowSignals
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name="Show Immediate Signals", Description="Generate signals when current bar hits green/red lines", Order=3, GroupName="Trading Settings")]
		public bool ShowImmediateSignals
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name="Show Delayed Signals", Description="Generate signals when next candle after HTF close hits previous green/red lines", Order=4, GroupName="Trading Settings")]
		public bool ShowDelayedSignals
		{ get; set; }

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="1.1 Level Color", Description="1.1 Level Color", Order=1, GroupName="Colors")]
		public Brush FibColor11
		{ get; set; }

		[Browsable(false)]
		public string FibColor11Serializable
		{
			get { return Serialize.BrushToString(FibColor11); }
			set { FibColor11 = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="1.08 Level Color", Description="1.08 Level Color", Order=2, GroupName="Colors")]
		public Brush FibColor108
		{ get; set; }

		[Browsable(false)]
		public string FibColor108Serializable
		{
			get { return Serialize.BrushToString(FibColor108); }
			set { FibColor108 = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="1.0 Level Color", Description="1.0 Level Color", Order=3, GroupName="Colors")]
		public Brush FibColor10
		{ get; set; }

		[Browsable(false)]
		public string FibColor10Serializable
		{
			get { return Serialize.BrushToString(FibColor10); }
			set { FibColor10 = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="0.9 Level Color", Description="0.9 Level Color", Order=4, GroupName="Colors")]
		public Brush FibColor09
		{ get; set; }

		[Browsable(false)]
		public string FibColor09Serializable
		{
			get { return Serialize.BrushToString(FibColor09); }
			set { FibColor09 = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="0.1 Level Color", Description="0.1 Level Color", Order=5, GroupName="Colors")]
		public Brush FibColor01
		{ get; set; }

		[Browsable(false)]
		public string FibColor01Serializable
		{
			get { return Serialize.BrushToString(FibColor01); }
			set { FibColor01 = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="0.0 Level Color", Description="0.0 Level Color", Order=6, GroupName="Colors")]
		public Brush FibColor00
		{ get; set; }

		[Browsable(false)]
		public string FibColor00Serializable
		{
			get { return Serialize.BrushToString(FibColor00); }
			set { FibColor00 = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="-0.08 Level Color", Description="-0.08 Level Color", Order=7, GroupName="Colors")]
		public Brush FibColorNeg08
		{ get; set; }

		[Browsable(false)]
		public string FibColorNeg08Serializable
		{
			get { return Serialize.BrushToString(FibColorNeg08); }
			set { FibColorNeg08 = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="-0.1 Level Color", Description="-0.1 Level Color", Order=8, GroupName="Colors")]
		public Brush FibColorNeg1
		{ get; set; }

		[Browsable(false)]
		public string FibColorNeg1Serializable
		{
			get { return Serialize.BrushToString(FibColorNeg1); }
			set { FibColorNeg1 = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="Label Text Color", Description="Label Text Color", Order=9, GroupName="Colors")]
		public Brush TextColor
		{ get; set; }

		[Browsable(false)]
		public string TextColorSerializable
		{
			get { return Serialize.BrushToString(TextColor); }
			set { TextColor = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="Bullish Candle Color", Description="Color for bullish MTF candles", Order=1, GroupName="MTF Candle Colors")]
		public Brush CandleBullishColor
		{ get; set; }

		[Browsable(false)]
		public string CandleBullishColorSerializable
		{
			get { return Serialize.BrushToString(CandleBullishColor); }
			set { CandleBullishColor = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="Bearish Candle Color", Description="Color for bearish MTF candles", Order=2, GroupName="MTF Candle Colors")]
		public Brush CandleBearishColor
		{ get; set; }

		[Browsable(false)]
		public string CandleBearishColorSerializable
		{
			get { return Serialize.BrushToString(CandleBearishColor); }
			set { CandleBearishColor = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="Candle Wick Color", Description="Color for MTF candle wicks", Order=3, GroupName="MTF Candle Colors")]
		public Brush CandleWickColor
		{ get; set; }

		[Browsable(false)]
		public string CandleWickColorSerializable
		{
			get { return Serialize.BrushToString(CandleWickColor); }
			set { CandleWickColor = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="Candle Border Color", Description="Color for MTF candle borders", Order=4, GroupName="MTF Candle Colors")]
		public Brush CandleBorderColor
		{ get; set; }

		[Browsable(false)]
		public string CandleBorderColorSerializable
		{
			get { return Serialize.BrushToString(CandleBorderColor); }
			set { CandleBorderColor = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[Range(0, 100)]
		[Display(Name="Candle Transparency", Description="Transparency level for MTF candle fill", Order=5, GroupName="MTF Candle Colors")]
		public int CandleTransparency
		{ get; set; }
		#endregion
	}
}
